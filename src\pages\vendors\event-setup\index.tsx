import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import useVendorStore from '@/store/vendorStore';
import Spinner from '@/components/Spinner';
import { VendorCompanyType } from '@/types';
import EditEventSetupCompany from '@/components/EditEventSetupCompany';

import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from '@/hooks/use-toast';
import axios from 'axios';
import AddEventSetupCompany from '@/components/AddEventSetupCompany';
import { domain } from '@/lib/utils';

const token = "Bearer 4872|W9hBxuIrteale3Mlwz1ToNhhKKkMehCdDNibpN9p";

const EventSetup: React.FC = () => {
    const [searchTerm, setSearchTerm] = useState('');
    const [editingVendor, setEditingVendor] = useState<VendorCompanyType | null>(null);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const { eventSetupCompany, setEventSetupCompany, loading } = useVendorStore();
    const [isLoading, setIsLoading] = useState<boolean>(false);

    useEffect(() => {
        const fetchData = async () => {
            try {
                await setEventSetupCompany();
                console.log(eventSetupCompany);
            } catch (error) {
                console.error('Error fetching vendors:', error);
            }
        };

        fetchData();
    }, [setEventSetupCompany]);

    const filteredVendors = eventSetupCompany?.filter((vendor) => {
        const term = searchTerm.toLowerCase();
        return (
            vendor.agency_name.toLowerCase().includes(term) ||
            vendor.agency_mail.toLowerCase().includes(term) ||
            vendor.agency_contact_number.toLowerCase().includes(term) ||
            vendor.city.some((city: string) => city.toLowerCase().includes(term)) ||
            (vendor.agency_website && vendor.agency_website.toLowerCase().includes(term))
        );
    });

    const handleDeleteEventSetupCompany = async (id: number) => {
        try {
            setIsLoading(true);
            const response = await axios.delete(`${domain}/api/destory-event-setup/${id}`, {
                headers: {
                    Authorization: token,
                }
            });
            if (response.data.status) {
                setEventSetupCompany();
                toast({
                    title: response.data.message || "Event Setup Company Deleted Successfully",
                    className: "bg-green-800 text-white",
                });
            }
        } catch (error) {
            console.error('Error deleting event setup company:', error);
            toast({
                title: "Error deleting event setup company",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    }

    if (isLoading || loading) {
        return <Spinner />
    }

    return (
        <div className="space-y-6 w-full h-full">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div className="relative max-w-md">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                        type="search"
                        placeholder="Search vendors..."
                        className="w-full rounded-lg bg-background pl-8"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                </div>
                <AddEventSetupCompany />
            </div>


            {filteredVendors?.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                    <p className="text-muted-foreground">
                        {searchTerm ? 'No vendors match your search.' : 'No vendors found.'}
                    </p>
                </div>
            ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {filteredVendors?.map((vendor) => (
                        <div key={vendor.id} className="rounded-lg border bg-card text-card-foreground flex flex-col justify-between shadow-sm p-6 space-y-4">
                            <div className="flex items-center space-x-4">
                                {/* For Logo */}
                                <div className='rounded-full border size-14 grid place-content-center overflow-clip'>
                                    {vendor.agency_logo ? (
                                        <img src={domain + '/' + vendor.agency_logo} alt="Logo" className="h-full w-full object-cover object-center" />
                                    ) : (
                                        <span className="text-white">{vendor.agency_name.charAt(0).toUpperCase()}</span>
                                    )}

                                </div>
                                <div>
                                    <h3 className="font-semibold capitalize">{vendor.agency_name}</h3>
                                    <p className="text-sm text-muted-foreground">{vendor.agency_mail}</p>
                                </div>
                            </div>

                            <div className="space-y-2 flex flex-col flex-1 text-sm">
                                <div className="flex items-center">
                                    <span className="w-24 text-muted-foreground">Contact</span>
                                    <span>{vendor.agency_contact_number}</span>
                                </div>
                                <div className="flex items-center">
                                    <span className="w-24 text-muted-foreground">Location</span>
                                    <div className="flex flex-wrap gap-1 capitalize">
                                        {vendor.city.map((city, i) => (
                                            <span key={i} className="px-2 py-0.5 bg-brandPrimary/10 rounded-md text-xs">
                                                {city}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                                {vendor.agency_website && (
                                    <div className="flex items-center">
                                        <span className="w-24 text-muted-foreground">Website</span>
                                        <a
                                            href={vendor.agency_website.startsWith('http') ? vendor.agency_website : `https://${vendor.agency_website}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            className="text-brandPrimary hover:underline"
                                        >
                                            {vendor.agency_website}
                                        </a>
                                    </div>
                                )}
                            </div>

                            <div className="flex justify-end gap-2 pt-2">

                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-brandPrimary hover:text-brandPrimary tracking-wider border-brandPrimary"
                                    onClick={() => {
                                        setEditingVendor(vendor);
                                        setIsEditDialogOpen(true);
                                    }}
                                >
                                    Edit
                                </Button>

                                <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            className="text-red-500 tracking-wider border-red-500 hover:text-red-500"
                                        >
                                            Delete
                                        </Button>
                                        {/* <Button
                                            variant="ghost"
                                            className={`p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-red-600/50 bg-red-500`}
                                        >
                                            <Trash />
                                        </Button> */}
                                    </AlertDialogTrigger>

                                    <AlertDialogContent>
                                        <AlertDialogHeader>
                                            <AlertDialogTitle>Delete Event Setup Company</AlertDialogTitle>
                                            <AlertDialogDescription>
                                                Are you sure you want to delete this event setup company?
                                            </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                                            <AlertDialogAction
                                                onClick={() => handleDeleteEventSetupCompany(vendor.id)}
                                                className={`text-white bg-red-500 hover:bg-red-600/80`}
                                            >
                                                Delete
                                            </AlertDialogAction>
                                        </AlertDialogFooter>
                                    </AlertDialogContent>
                                </AlertDialog>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {editingVendor && (
                <EditEventSetupCompany
                    vendor={editingVendor}
                    isOpen={isEditDialogOpen}
                    onClose={() => {
                        setIsEditDialogOpen(false);
                        setEditingVendor(null);
                    }}
                />
            )}
        </div>
    );
};

export default EventSetup;
