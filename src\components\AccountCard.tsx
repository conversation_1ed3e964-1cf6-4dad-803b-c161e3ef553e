// import { domain } from '@/lib/utils';
import { UserAccount } from '@/types';
import React, { useState } from 'react';
import { Button } from './ui/button';

import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    <PERSON>alogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";
import axios from 'axios';
import Spinner from './Spinner';
import { Switch } from './ui/switch';
import { cn, domain } from '@/lib/utils';

interface Permissions {
    search_people: 0 | 1;
    vendor: 0 | 1;
    wallet: 0 | 1;
}

const AccountCard: React.FC<UserAccount> = (props) => {
    const [permissions, setPermissions] = useState<Permissions>({
        search_people: 0,
        vendor: 0,
        wallet: 0
    });

    const [permissionsLoading, setPermissionsLoading] = useState<boolean>(false);

    const handleGetAccountPermissions = async (userId: number) => {
        setPermissionsLoading(true);
        try {
            const response = await axios.get(`${domain}/api/get-feature-permission/${userId}`);
            if (response.data.status === 200) {
                const data = response.data.data[0] || response.data.data;
                setPermissions({
                    search_people: data.search_people as 0 | 1,
                    vendor: data.vendor as 0 | 1,
                    wallet: data.wallet as 0 | 1
                });
            }
        } catch (error) {
            console.error("Error fetching account permissions:", error);
            return;

        } finally {
            setPermissionsLoading(false);
        }
    }

    const handlePermissionChange = async (permissionType: keyof Permissions, newValue: number) => {
        try {
            // Optimistically update the UI
            setPermissions(prev => ({
                ...prev,
                [permissionType]: newValue
            }));

            // Make the API call to update the permission
            await axios.post(`${domain}/api/update-feature-permission/${props.id}`, {
                ...permissions,
                [permissionType]: newValue,
                _method: 'PUT'
            });

        } catch (error) {
            console.error(`Error updating ${permissionType} permission:`, error);
            // Revert on error
            setPermissions(prev => ({
                ...prev,
                [permissionType]: newValue === 1 ? 0 : 1
            }));
        } finally {
            setPermissionsLoading(false);
        }
    }

    return (
        <div className="rounded-lg border bg-card text-card-foreground flex flex-col justify-between shadow-sm p-6 space-y-4">
            <div className="flex items-center space-x-4">
                {/* For Logo */}
                <div className='rounded-full border size-14 min-w-14 min-h-14 grid place-content-center overflow-clip'>
                    {props.company_logo ? (
                        <img src={domain + '/' + props.company_logo} alt="Logo" className="h-full w-full object-cover object-center" />
                    ) : (
                        <span className="text-white">{props.first_name.charAt(0).toUpperCase()}{props.last_name.charAt(0).toUpperCase()}</span>
                    )}

                </div>
                <div>
                    <h3 className="font-semibold capitalize">{props.first_name} {props.last_name}</h3>
                    <p className="text-sm text-muted-foreground overflow-hidden text-ellipsis">{props.email}</p>
                </div>
            </div>

            <div className="space-y-2 flex flex-col flex-1 text-sm">
                <div className="flex items-center">
                    <span className="w-24 text-muted-foreground">Contact</span>
                    <span>{props.mobile_number}</span>
                </div>

                <Dialog>
                    <DialogTrigger asChild>
                        <Button onClick={() => handleGetAccountPermissions(props.id)} variant="outline">View Details</Button>
                    </DialogTrigger>
                    {permissionsLoading ?
                        <DialogContent className='h-96 grid place-content-center'>
                            <DialogTitle></DialogTitle>
                            <DialogDescription></DialogDescription>
                            <Spinner />
                        </DialogContent>
                        :
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle className='text-2xl font-semibold'>{props.first_name} {props.last_name}</DialogTitle>
                                <DialogDescription className='mt-4'>
                                    This account has these following permissions.
                                </DialogDescription>
                                <div className='space-y-2 !mt-8'>
                                    <div className='flex items-center justify-between py-2'>
                                        <span>Vendors</span>
                                        <Switch
                                            checked={permissions.vendor === 1}
                                            className={cn(permissions.vendor === 1 ? '!bg-brandPrimary' : '')}
                                            onCheckedChange={(checked) => handlePermissionChange('vendor', checked ? 1 : 0)}
                                        />
                                    </div>
                                    <div className='flex items-center justify-between py-2'>
                                        <span>Search People</span>
                                        <Switch
                                            checked={permissions.search_people === 1}
                                            className={cn(permissions.search_people === 1 ? '!bg-brandPrimary' : '')}
                                            onCheckedChange={(checked) => handlePermissionChange('search_people', checked ? 1 : 0)}
                                        />
                                    </div>
                                    <div className='flex items-center justify-between py-2'>
                                        <span>Wallet</span>
                                        <Switch
                                            checked={permissions.wallet === 1}
                                            className={cn(permissions.wallet === 1 ? '!bg-brandPrimary' : '')}
                                            onCheckedChange={(checked) => handlePermissionChange('wallet', checked ? 1 : 0)}
                                        />
                                    </div>
                                </div>
                            </DialogHeader>
                        </DialogContent>}
                </Dialog>
            </div>
        </div>
    )
}

export default AccountCard;
