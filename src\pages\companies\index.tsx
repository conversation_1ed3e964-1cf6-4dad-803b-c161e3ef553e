import EditDialogBox from '@/components/EditDialogBox';
import { Input } from '@/components/ui/input';
import { Company } from '@/types';
import React, { useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import {
  Dialog,
  DialogContent,
  DialogClose,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

import { DialogDescription } from '@radix-ui/react-dialog';
import { Label } from "@/components/ui/label";

import Spinner from '@/components/Spinner'; // Import Spinner component for loading indicator
import axios from 'axios';
import useCompanyStore from '@/store/companyStore';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Image, Plus, Save, View } from 'lucide-react';
import DialogBox from '@/components/DialogBox';
import DropZone from '@/components/DropZone';
import { domain2 } from '@/lib/utils';
import { Select, SelectValue, SelectContent, SelectItem, SelectTrigger, SelectGroup, SelectLabel } from '@/components/ui/select';

const Companies: React.FC = () => {
  const intialAllCompanies = useCompanyStore(state => state.totalCompanies);  // This is your initial total companies count
  const [singleCompanyLoading, setSingleCompanyLoading] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [companyData, setCompanyData] = useState<Company[] | undefined>(undefined);
  const [filteredData, setFilteredData] = useState<string>("");
  const [pageNumber, setPageNumber] = useState<number>(1);  // Start with page 1
  const [industry, setIndustry] = useState<string>("");
  const [employeeSize, setEmployeeSize] = useState<string>("");
  const [logo, setLogo] = useState<string>("all");
  const [totalPages, setTotalPages] = useState<number>(Math.ceil(intialAllCompanies / 50));  // Default page calculation

  const [formData, setFormData] = useState({
    company: '',
    industry: '',
    companySize: '',
    profileUrl: "",
    headquater: '',
    website: '',
    mappedTo: '',
    specialities: '',
    overview: '',
    companyLogo: "",
    brochure: "",
    videoLink: "",
  });

  const [singleCompany, setSingleCompany] = useState<Company | null>(null);

  const handleChange = (e: any) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const pageSize = 50;  // Companies per page

  const fetchCompanies = async (page?: number, search?: string, industry?: string, employeeSize?: string, logo?: string | undefined) => {
    const encodedSearch = encodeURIComponent(search || "");
    const res = await axios.get(`${domain2}/api/mapping/v1/company-master/all-company?page=${page}&search=${encodedSearch}&industry=${industry}&employeeSize=${employeeSize}&logo=${logo}`, {
      headers: {
        "Content-Type": "application/json"
      }
    });

    const fetchedCompanies = res.data.data.companies;
    const fetchedTotalCompanies = res.data.data.totalCompanies;  // This should come from the API

    setCompanyData(fetchedCompanies);

    // Calculate total pages based on fetched total companies and the page size
    setTotalPages(Math.ceil(fetchedTotalCompanies / pageSize));
  }

  const fetchSingleCompany = async (id: string) => {
    setSingleCompanyLoading(true);
    const response = await axios.get(`${domain2}/api/mapping/v1/company-master/view-company/${id}`);

    if (response.data.status) {
      console.log("The data is: ", response.data.data);
      setSingleCompany(response.data.data);
      setSingleCompanyLoading(false);
    }
  }

  useEffect(() => {
    setLoading(true);
    if (logo === "all") {
      fetchCompanies(pageNumber, filteredData, industry, employeeSize);
    } else {
      fetchCompanies(pageNumber, filteredData, industry, employeeSize, logo);
    }
    setLoading(false);
  }, [pageNumber, filteredData, loading, industry, employeeSize, logo]);

  const addCompany = async () => {
    // Example validation
    // if (!formData.company || !formData.industry) {
    //   alert('Please fill out the required fields: Name and Industry.');
    //   return;
    // }

    try {
      setLoading(true);
      const res = await axios.post(`${domain2}/api/mapping/v1/company-master/add-company`, formData, {
        headers: {
          "Content-Type": "application/json",
        }
      });

      // Reset form
      setFormData({
        company: '',
        industry: '',
        companySize: '',
        headquater: '',
        website: '',
        mappedTo: '',
        profileUrl: "",
        specialities: '',
        overview: '',
        companyLogo: "",
        brochure: "",
        videoLink: ""
      });

      if (res.data.status) {
        toast({
          title: "Company Added Successfully",
          className: "bg-green-800 text-white",
        });
      }
    } catch (error) {
      toast({
        title: "Something went wrong!!!",
        variant: "destructive",
      })
    } finally {
      setLoading(false);
    }
  };

  const deleteCompany = async (id: string) => {
    setLoading(true);
    try {
      const res = await axios.delete(`${domain2}/api/mapping/v1/company-master/delete-company/${id}`);
      if (res.data.status) {
        toast({
          title: "Company Deleted Successfully",
          className: "bg-green-800 text-white",
        })
      }
    } catch (error) {
      toast({
        title: "Something went wrong!!!",
        variant: "destructive",
      })
    } finally {
      setLoading(false);
    }
  }

  const handleSendBack = async (company: string) => {
    const response = await axios.post(`${domain2}/api/mapping/v1/company-master/send-back-new-company-to-unmpapped-or-new-company`, {
      company
    }, {
      headers: {
        "Content-Type": "application/json"
      }
    });

    if (response.data.status) {
      fetchCompanies(pageNumber, "");
      toast({
        title: response.data.message,
        className: "bg-green-800 text-white"
      })
    } else {
      toast({
        title: response.data.message,
        variant: "destructive"
      });
    }
  }

  if (loading) {
    return <Spinner />
  }

  return (
    <div className='w-full'>

      {/* Add Company */}
      <div className="flex flex-wrap-reverse gap-3 justify-between mt-5 w-full mb-3">
        <div className='flex gap-3'>
          <Select onValueChange={(value) => setLogo(value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter By Logo" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Logo Status</SelectLabel>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="true">Has Logo</SelectItem>
                <SelectItem value="false">No Logo</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <Input
            type="text"
            className="max-w-60"
            onChange={(e) => setFilteredData(e.target.value)}
            placeholder="Filter by company name"
          />
          <Input
            type="text"
            className="max-w-60"
            onChange={(e) => setIndustry(e.target.value)}
            placeholder="Filter by industry"
          />
          <Input
            type="text"
            className="max-w-60"
            onChange={(e) => setEmployeeSize(e.target.value)}
            placeholder="Filter by employee size"
          />
        </div>
        <div className="flex gap-3">
          <Dialog>
            <DialogTrigger asChild>
              <Button className="bg-brandPrimary hover:bg-brandPrimaryDark text-white">
                Add Company <Plus />
              </Button>
            </DialogTrigger>

            <DialogContent className="max-w-xl max-h-[30rem] overflow-scroll py-0 px-3 sm:px-5">
              <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                Add Company
              </DialogTitle>

              <div className="space-y-5">
                {/* First Row */}
                <div className="flex flex-col min-[460px]:flex-row justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="company" className="text-sm font-normal">Name <span className='text-destructive'>*</span></Label>
                    <Input id="company" type="text" value={formData.company} onChange={handleChange} placeholder="Company Name" />
                  </div>
                  <div className="w-full">
                    <Label htmlFor="industry" className="text-sm font-normal">Industry <span className='text-destructive'>*</span></Label>
                    <Input id="industry" type="text" value={formData.industry} onChange={handleChange} placeholder="Industry" />
                  </div>
                </div>

                {/* Second Row */}
                <div className="flex justify-between flex-col min-[460px]:flex-row gap-5">
                  <div className="w-full">
                    <Label htmlFor="companySize" className="text-sm font-normal">Size <span className='text-destructive'>*</span></Label>
                    <Input id="companySize" type="text" value={formData.companySize} onChange={handleChange} placeholder="Employee Size" />
                  </div>
                  <div className="w-full">
                    <Label htmlFor="headquater" className="text-sm font-normal">Headquarter <span className='text-destructive'>*</span></Label>
                    <Input id="headquater" type="text" value={formData.headquater} onChange={handleChange} placeholder="Location" />
                  </div>
                </div>

                {/* Third Row */}
                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="website" className="text-sm font-normal">Website <span className='text-destructive'>*</span></Label>
                    <Input id="website" type="text" value={formData.website} onChange={handleChange} placeholder="Website" />
                  </div>
                </div>

                {/* Third Row */}
                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="profileUrl" className="text-sm font-normal">LinkedIn Profile URL <span className='text-destructive'>*</span></Label>
                    <Input id="profileUrl" type="text" value={formData.profileUrl} onChange={handleChange} placeholder="Profile URL" />
                  </div>
                </div>

                {/* Fourth Row */}
                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="mappedTo" className="text-sm font-normal">Mapped To <span className='text-destructive'>*</span></Label>
                    <Input id="mappedTo" type="text" value={formData.mappedTo} onChange={handleChange} placeholder="Mapped Companies" />
                  </div>
                </div>

                {/* Fifth Row */}
                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="specialities" className="text-sm font-normal">Specialities <span className='text-destructive'>*</span></Label>
                    <textarea
                      id="specialities"
                      value={formData.specialities}
                      onChange={handleChange}
                      rows={4}
                      className="bg-background focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-brandPrimary resize-none p-2 border text-sm w-full rounded"
                    />
                  </div>
                </div>

                {/* Sixth Row */}
                <div className="flex justify-between gap-5">
                  <div className="w-full">
                    <Label htmlFor="overview" className="text-sm font-normal">Overview <span className='text-destructive'>*</span></Label>
                    <textarea
                      id="overview"
                      value={formData.overview}
                      onChange={handleChange}
                      rows={4}
                      className="bg-background resize-none focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-brandPrimary p-2 border text-sm w-full rounded"
                    />
                  </div>
                </div>

                <div className='flex flex-col !gap-5'>
                  {/* Seventh Row */}
                  <div className='w-full'>
                    <Label htmlFor="companyLogo" className="text-sm font-normal">Company Logo (Optional)</Label>
                    <Input id="companyLogo" type="text" value={formData.companyLogo} onChange={handleChange} placeholder="Company Logo" />
                  </div>

                  {/* Eighth Row */}
                  <div className='h-40 !w-full'>
                    <Label htmlFor="overview" className="text-sm font-normal">Upload Brochure (Optional)</Label>
                    <DropZone className='w-full h-40' />
                  </div>

                  {/* Nithth Row */}
                  <div className='w-full !mt-5'>
                    <Label htmlFor="videoLink" className="text-sm font-normal">Company Video Link (Optional)</Label>
                    <Input id="videoLink" type="text" value={formData.videoLink} onChange={handleChange} placeholder="Video Link" />
                  </div>
                </div>
              </div>

              <div className="py-5 bg-background sticky bottom-0">
                <Button onClick={addCompany} className="bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full">
                  Add Company <Save />
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div>
        {loading ? (
          <div className="flex justify-center items-center h-48">
            <Spinner />
          </div>
        ) : (
          <Table className='text-base'>
            <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
              <TableRow>
                <TableHead className='text-white'>Logo</TableHead>
                <TableHead className='text-white'>Company Name</TableHead>
                <TableHead className='text-white'>Industry Name</TableHead>
                <TableHead className='text-white'>Employee Size</TableHead>
                <TableHead className="text-white !w-fit">Mapped Companies</TableHead>
                <TableHead className="text-white">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {companyData?.map((data: Company) => (
                <TableRow key={data._id}>

                  <TableCell>
                    {/* <div>
                      {data?.companyLogo ? (
                        <img src={data.companyLogo} alt="Logo" className='size-5 bg-cover bg-center mx-auto' />
                      ) : (
                        <Image className="size-5 mx-auto" />
                      )}
                    </div> */}
                    <Avatar className="size-6 rounded-none">
                      <AvatarImage src={data.companyLogo} className='size-6 rounded-none bg-cover bg-center'/>
                      <AvatarFallback className='size-6 rounded-none bg-transparent'><Image className="size-5 mx-auto bg-cover bg-center" /></AvatarFallback>
                    </Avatar>
                  </TableCell>
                  <TableCell>{data.company}</TableCell>
                  <TableCell>{data.industry}</TableCell>
                  <TableCell>{data.companySize}</TableCell>
                  <TableCell>{data.mappedTo.join(', ')}</TableCell>
                  <TableCell className="flex justify-end gap-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button
                          variant={"ghost"}
                          onClick={() => fetchSingleCompany(data._id)}
                          className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-green-600/50 bg-green-500"
                        >
                          <View />
                        </Button>
                      </DialogTrigger>
                      {!singleCompanyLoading ? <DialogContent className="max-w-xl max-h-[30rem] overflow-scroll py-0">
                        <DialogDescription></DialogDescription>
                        <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                          Company Details
                        </DialogTitle>

                        <div className="space-y-5">

                          {/* Company Logo */}
                          {/* <div className='h-14 mx-auto'>
                            {singleCompany?.companyLogo ? (
                              <img src={singleCompany.companyLogo} alt="Company Logo" className='h-14 mx-auto' />
                            ) : (
                              <Image className="size-14 mx-auto" />
                            )}
                          </div> */}
                          <Avatar className="size-20 mx-auto rounded-none">
                            <AvatarImage src={singleCompany?.companyLogo} />
                            <AvatarFallback className='bg-transparent size-20 !rounded-none'><Image className="size-20 bg-cover bg-center" /></AvatarFallback>
                          </Avatar>

                          <div className="flex flex-col min-[560px]:flex-row justify-between gap-5">
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                Name
                              </span>
                              <p>{singleCompany?.company || "Not Available"}</p>
                            </div>
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                Company's Industry
                              </span>
                              <p>{singleCompany?.industry || "Not Available"}</p>
                            </div>
                          </div>

                          <div className="flex flex-col min-[560px]:flex-row justify-between gap-5">
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                Size
                              </span>
                              <p>{singleCompany?.companySize || "Not Available"}</p>
                            </div>
                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                Company's Headquarter
                              </span>
                              <p>{singleCompany?.headquarters || "Not Available"}</p>
                            </div>
                          </div>

                          <div className="w-full flex flex-col justify-between items-center gap-5">
                            <div className='w-full'>
                              <span className="text-sm text-muted-foreground font-normal">
                                Website
                              </span>
                              <p>{singleCompany?.website || "Not Available"}</p>
                            </div>

                            <div className="w-full">
                              <span className="text-sm text-muted-foreground font-normal">
                                LinkedIn Profile URL
                              </span>
                              <p>{singleCompany?.profileUrl || "Not Available"}</p>
                            </div>
                          </div>

                          <div className="w-full">
                            <span className="text-sm text-muted-foreground font-normal">
                              Mapped Companies
                            </span>
                            <p>{singleCompany?.mappedTo?.length! > 0 ? singleCompany?.mappedTo?.join(", ") : "Not Available"}</p>
                          </div>

                          <div className="w-full">
                            <span className="text-sm text-muted-foreground font-normal">
                              Specialities
                            </span>
                            <p>{singleCompany?.specialities || "Not Available"}</p>
                          </div>

                          <div className="w-full">
                            <span className="text-sm text-muted-foreground font-normal">
                              Overview
                            </span>
                            <p>{singleCompany?.overview || "Not Available"}</p>
                          </div>
                        </div>

                        <div className='sticky py-5 bg-background bottom-0'>
                          <DialogClose asChild className="bg-brandPrimary rounded-md py-2 bottom-5 text-center bg-background hover:bg-brandPrimaryDark cursor-pointer text-white w-full">
                            <div className="bg-background h-full w-full sticky bottom-0">
                              Ok
                            </div>
                          </DialogClose>
                        </div>
                      </DialogContent> :
                        <DialogContent className="max-w-xl max-h-[30rem] h-full overflow-scroll py-0">
                          <DialogDescription></DialogDescription>
                          <DialogTitle></DialogTitle>
                          <div className='h-full w-full'>
                            <Spinner />
                          </div>
                        </DialogContent>}
                    </Dialog>

                    <EditDialogBox type='company' data={data} title='Edit Company' />
                    <DialogBox buttonText='Delete' trigger='delete' title={`Are you sure want to delete ${data.company} ?`} description='This will permanently delete from the database' onSubmit={() => deleteCompany(data._id)} />

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button className='px-3 py-1 ml-3 text-sm bg-brandPrimary hover:bg-brandPrimaryDark text-white'>Send Back</Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure want to send {data.company} back to unmapped/new pool?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={() => handleSendBack(data.company)}>Continue</AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>

                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            {/* Previous Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            {/* First page */}
            <PaginationItem className="cursor-pointer">
              <PaginationLink
                onClick={() => setPageNumber(1)}
                isActive={pageNumber === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>

            {/* Ellipsis before the current range */}
            {pageNumber > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Dynamic Page Numbers */}
            {Array.from({ length: 3 }, (_, index) => {
              const page = pageNumber - 1 + index;
              if (page > 1 && page < totalPages) {
                return (
                  <PaginationItem className="cursor-pointer" key={page}>
                    <PaginationLink
                      isActive={pageNumber === page}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              return null;
            })}

            {/* Ellipsis after the current range */}
            {pageNumber < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Last page */}
            {totalPages > 1 && (
              <PaginationItem className="cursor-pointer">
                <PaginationLink
                  onClick={() => setPageNumber(totalPages)}
                  isActive={pageNumber === totalPages}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Next Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>

      </div>
    </div>
  );
};

export default Companies;