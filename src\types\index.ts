export interface ApiType {
    id: number;
    parent_id: number;
    name: string;
    created_at: string | null;
    updated_at: string | null;
}

export interface User {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string | null;
    mobile_number: string;
    pincode: string;
    notifications: number;
    tnc: number;
    designation: number;
    company: number;
    company_name: string;
    company_logo: string;
    address: string;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    image: string | null;
    uuid: string;
    designation_name: string;
}

export interface Company {
    _id: string;
    company: string;
    website: string;
    industry: string;
    companySize: string;
    headquarters: string;
    specialities: string;
    overview: string;
    mappedTo: string[];
    companyLogo: string;
    profileUrl: string;
}

export interface Customer {
    addedBy: string;
    appVersion: string;
    blocked: string[];
    blockedBy: string[];
    city: string;
    cityId: string;
    company: string;
    createdAt: string;
    designation: string;
    deviceName: string;
    deviceToken: string;
    deviceType: string;
    deviceVersion: string;
    emailId: string;
    first_name: string;
    googleId: string;
    images: string;
    industryName: string;
    isDeactivate: number;
    isOldUser: boolean;
    last_name: string;
    latitude: string;
    linkedInAccessToken: string;
    linkedInId: string;
    longitude: string;
    mobileNumber: number;
    preferred_skills: string;
    profileImage: string;
    role: string;
    searchDistanceinKm: string;
    shareLastSeen: string;
    showEmail: boolean;
    showMobile: boolean;
    status: string;
    updatedAt: string;
    whatsAppNotifications: string;
    _id: string;
}


export interface BlockRecord {
    id: number;
    blocker_user_id: string;
    user_id: string;
    reason: string;
    status: string;
    created_at: string;
    updated_at: string;
    type: number;
}

interface Person {
    _id: string;
    first_name: string;
    last_name: string;
}

export interface Notification {
    _id: string;
    user_id: Person;
    from_user_id: string;
    title: string;
    body: string;
    data: string; // this can be parsed into NotificationData if needed
    isRead: number;
    type: number;
    createdAt: string;
    updatedAt: string;
    __v: number;
}

export interface AppUser {
    _id: string;
    first_name: string;
    last_name: string;
    job_title: string;
    company_name: string;
    industry: string;
    email: string;
    phone_number: string;
    alternate_mobile_number: string;
    website: string;
    employee_size: string;
    company_turn_over: string;
    linkedin_page_link: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
    country: string;
    job_function: string;
}

export interface JobCompany {
    created_at: string;
    id: number;
    name: string;
    parent_id: number;
    updated_at: string;
}

export interface Designation {
    _id: string;
    designation: string;
    mappedTo: string[];
}

export interface MapDesignationType {
    createdAt: string;
    designation: string;
    updatedAt: string;
    __v: number;
    _id: string;
}

export interface MapCompanyType {
    company: string;
    createdAt: string;
    isMapped: boolean;
    mappedWith: string;
    updatedAt: string;
    __v: number;
    _id: string;
}
export interface MapInstituteType {
    institute: string;
    createdAt: string;
    isMapped: boolean;
    mappedWith: string;
    updatedAt: string;
    __v: number;
    _id: string;
}

export interface MapDesignationData {
    _id: string;
    designation: string;
    isMapped: boolean;
    mappedWith: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
}

export interface Publisher {
    _id: string;
    name: string;
    url: string;
    da: number;
    visits: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
}

export interface Institute {
    _id: string;
    aisheCode: string;
    name: string;
    state: string;
    district: string;
    websiteUrl: string;
    YOE: string;
    location: string;
    collegeType: string;
    universityName: string;
    universityType: string;
    administrativeMinistry: string;
    management: string;
    mappedTo: string[];
}


export interface VendorCompanyType {
    id: number;
    uuid: string;
    agency_name: string;
    agency_mail: string;
    agency_contact_number: string;
    agency_alternate_mail: string | null;
    agency_alternate_contact_number: string | null;
    agency_website: string;
    agency_logo: string | File;
    city: string[];
}

export interface UserAccount {
    id: number;
    uuid: string;
    first_name: string;
    last_name: string;
    email: string;
    mobile_number: string;
    company_name: string;
    company_logo: string;
}