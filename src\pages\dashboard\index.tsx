import Card from '@/components/Card';
import Spinner from '@/components/Spinner';
import { domain2 } from '@/lib/utils';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

interface CardProps {
  title: string;
  count: number;
}

interface ApiData {
  totalCompanies: number;
  totalDesignations: number;
  totalInstitutes: number;
  totalPeoples: number;
  totalUsers: number;
  all_event: number;
  all_attendee: number;
  all_organiser_user: number;
  all_audience_aquisition_agency: number;
  all_event_setup_agency: number;
  all_gifting_partner_agency: number;
}

const mapApiDataToCounts = (data: ApiData): CardProps[] => [
  { title: 'Total Companies', count: data.totalCompanies },
  { title: 'Total Designations', count: data.totalDesignations },
  { title: 'Total Institutes', count: data.totalInstitutes },
  { title: 'Total Peoples', count: data.totalPeoples },
  { title: 'Total Users', count: data.totalUsers },
  { title: 'Total Events', count: data.all_event },
  { title: 'Total Attendees', count: data.all_attendee },
  { title: 'Total Organiser Users', count: data.all_organiser_user },
  { title: 'Audience Acquisition Agencies', count: data.all_audience_aquisition_agency },
  { title: 'Event Setup Agencies', count: data.all_event_setup_agency },
  { title: 'Gifting Partner Agencies', count: data.all_gifting_partner_agency }
];

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [cardData, setCardData] = useState<CardProps[]>([
    { title: 'Total Companies', count: 0 },
    { title: 'Total Designations', count: 0 },
    { title: 'Total Institutes', count: 0 },
    { title: 'Total Peoples', count: 0 },
    { title: 'Total Users', count: 0 },
    { title: 'Total Events', count: 0 },
    { title: 'Total Attendees', count: 0 },
    { title: 'Total Organiser Users', count: 0 },
    { title: 'Audience Acquisition Agencies', count: 0 },
    { title: 'Event Setup Agencies', count: 0 },
    { title: 'Gifting Partner Agencies', count: 0 }
  ]);

  useEffect(() => {
    setLoading(true);
    axios.post(`${domain2}/api/mapping/v1/people/get-all-counts`, {}, {
      headers: {
        'Content-Type': 'application/json',
      }
    }).then(res => {
      if (res.data.status) {
        const mappedData = mapApiDataToCounts(res.data.data);
        setCardData(mappedData);
      }
      setLoading(false);
    }).catch(err => {
      console.error('Error fetching counts:', err);
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <Spinner />;
  }

  return (
    <div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'>
      {cardData.map((item, index) => (
        <Card key={index} title={item.title} count={item.count} className='max-w-60' />
      ))}
    </div>
  );
}

export default Dashboard;