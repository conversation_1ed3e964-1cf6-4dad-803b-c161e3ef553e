import AccountCard from '@/components/AccountCard';
import Spinner from '@/components/Spinner';
import usePermissionsStore from '@/store/usePermissionsStore';
import React, { useEffect } from 'react';

const Permissions: React.FC = () => {
    const { userAccounts, getUserAccounts, loading } = usePermissionsStore(state => state);

    useEffect(() => {
        getUserAccounts();
    }, [getUserAccounts]);

    if (loading) {
        return <Spinner />;
    }

    return (
        <div className='min-w-full min-h-full'>
            {
                userAccounts.length === 0 ? (
                    <div className='min-w-full h-full grid place-content-center'>
                        <p className='text-muted-foreground'>No user accounts found.</p>
                    </div>
                ) : (
                    <div className='grid grid-cols-2 lg:grid-cols-3 gap-4'>
                        {userAccounts.map((account, index) => (
                            <div key={index}>
                                <AccountCard {...account} />
                            </div>
                        ))}
                    </div>
                )
            }
        </div>
    )
}

export default Permissions;
