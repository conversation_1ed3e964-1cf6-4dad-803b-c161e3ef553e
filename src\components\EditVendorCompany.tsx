import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { VendorCompanyType } from '@/types';
import { Input } from './ui/input';
import { Label } from './ui/label';
import axios from 'axios';
import { toast } from '@/hooks/use-toast';
import useVendorStore from '@/store/vendorStore';
import Spinner from './Spinner';
import { domain } from '@/lib/utils';

const token = "Bearer 4872|W9hBxuIrteale3Mlwz1ToNhhKKkMehCdDNibpN9p";

interface EditVendorCompanyProps {
    vendor: VendorCompanyType;
    isOpen: boolean;
    onClose: () => void;
}

const EditVendorCompany: React.FC<EditVendorCompanyProps> = ({ vendor, isOpen, onClose }) => {
    const { setAcquisitionCompany, loading } = useVendorStore();
    const [form, setForm] = useState<Omit<VendorCompanyType, 'id' | 'uuid'>>({
        agency_name: "",
        agency_mail: "",
        agency_contact_number: "",
        agency_alternate_mail: "",
        agency_alternate_contact_number: "",
        agency_website: "",
        agency_logo: "",
        city: [],
    });

    const [cities, setCities] = useState<string>("");
    const [logoFile, setLogoFile] = useState<File | null>(null);
    const [logoPreview, setLogoPreview] = useState<string>("");

    useEffect(() => {
        if (vendor) {
            setForm({
                agency_name: vendor.agency_name || "",
                agency_mail: vendor.agency_mail || "",
                agency_contact_number: vendor.agency_contact_number || "",
                agency_alternate_mail: vendor.agency_alternate_mail || "",
                agency_alternate_contact_number: vendor.agency_alternate_contact_number || "",
                agency_website: vendor.agency_website || "",
                agency_logo: vendor.agency_logo || "",
                city: vendor.city || [],
            });
            setCities(vendor.city?.join(', ') || "");
            if (vendor.agency_logo) {
                setLogoPreview(`${domain}/${vendor.agency_logo}`);
            }
        }
    }, [vendor]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setForm(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setLogoFile(file);
            const reader = new FileReader();
            reader.onloadend = () => {
                setLogoPreview(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleCitiesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setCities(value);
        setForm(prev => ({
            ...prev,
            city: value.split(',').map(city => city.trim()).filter(Boolean)
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        const formData = new FormData();
        formData.append('agency_name', form.agency_name);
        formData.append('agency_mail', form.agency_mail);
        formData.append('agency_contact_number', form.agency_contact_number);
        formData.append('agency_alternate_mail', form.agency_alternate_mail || "");
        formData.append('agency_alternate_contact_number', form.agency_alternate_contact_number || "");
        formData.append('agency_website', form.agency_website || "");

        // Append the logo file if it exists
        if (logoFile) {
            formData.append('agency_logo', logoFile);
        }

        // Append cities as JSON string
        formData.append('city', JSON.stringify(form.city));
        formData.append('_method', 'PUT');

        try {
            const response = await axios.post(
                `${domain}/api/update-audience-acquisition/${vendor.id}`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                        'Authorization': token
                    }
                }
            );

            if (response.data.status === 200) {
                setAcquisitionCompany();
                toast({
                    title: response.data.message || "Vendor updated successfully",
                    variant: "default",
                    className: "bg-green-800 text-white"
                });
                onClose();
            } else {
                throw new Error(response.data.message || "Failed to update vendor");
            }
        } catch (error: any) {
            console.error('Error updating vendor:', error);
            toast({
                title: error.response?.data?.message || "Failed to update vendor",
                variant: "destructive",
                className: "bg-red-800 text-white"
            });
        }
    };

    if (loading) {
        return <Spinner />;
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                <form onSubmit={handleSubmit}>
                    <DialogHeader className="mb-6">
                        <DialogTitle>Edit Vendor</DialogTitle>
                        <DialogDescription>
                            Update the vendor details below.
                        </DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4 py-2">
                        {/* Logo Upload */}
                        <div className="flex flex-col items-center space-y-1 mb-2">
                            <Label className="text-center block">Company Logo <span className="text-red-500">*</span></Label>
                            <div className="relative group">
                                <div className="relative h-16 w-16 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden bg-gray-50">
                                    {logoPreview ? (
                                        <img
                                            src={logoPreview}
                                            alt="Logo preview"
                                            className="h-full w-full object-cover"
                                        />
                                    ) : (
                                        <div className="text-gray-400">
                                            <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                        </div>
                                    )}
                                </div>
                                <label
                                    htmlFor="logo-upload"
                                    className="absolute inset-0 flex items-center justify-center cursor-pointer opacity-0 group-hover:opacity-100 bg-black/50 rounded-full transition-opacity"
                                >
                                    <span className="text-white text-sm font-medium">
                                        {logoPreview ? 'Change' : 'Upload'}
                                    </span>
                                    <input
                                        id="logo-upload"                                       type="file"
                                        className="sr-only"
                                        accept="image/png, image/jpeg, image/jpg, image/svg+xml"
                                        onChange={handleLogoChange}
                                    />
                                </label>
                            </div>
                            <p className="text-[10px] text-muted-foreground text-center">
                                PNG, JPG, JPEG, or SVG
                            </p>
                        </div>

                        {/* Form Fields */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="space-y-1">
                                <Label htmlFor="agency_name">Agency Name <span className="text-red-500">*</span></Label>
                                <Input
                                    id="agency_name"
                                    name="agency_name"
                                    placeholder="Enter agency name"
                                    value={form.agency_name}
                                    onChange={handleChange}
                                    required
                                />
                            </div>

                            <div className="space-y-1">
                                <Label htmlFor="agency_mail">Email <span className="text-red-500">*</span></Label>
                                <Input
                                    id="agency_mail"
                                    type="email"
                                    name="agency_mail"
                                    placeholder="Enter email"
                                    value={form.agency_mail}
                                    onChange={handleChange}
                                    required
                                />
                            </div>

                            <div className="space-y-1">
                                <Label htmlFor="agency_contact_number">Contact Number <span className="text-red-500">*</span></Label>
                                <Input
                                    id="agency_contact_number"
                                    name="agency_contact_number"
                                    placeholder="Enter contact number"
                                    value={form.agency_contact_number}
                                    onChange={handleChange}
                                    required
                                />
                            </div>

                            <div className="space-y-1">
                                <Label htmlFor="agency_alternate_contact_number">Alternate Contact</Label>
                                <Input
                                    id="agency_alternate_contact_number"
                                    name="agency_alternate_contact_number"
                                    placeholder="Enter alternate contact number"
                                    value={form.agency_alternate_contact_number || ''}
                                    onChange={handleChange}
                                />
                            </div>

                            <div className="space-y-1">
                                <Label htmlFor="agency_alternate_mail">Alternate Email</Label>
                                <Input
                                    id="agency_alternate_mail"
                                    type="email"
                                    name="agency_alternate_mail"
                                    placeholder="Enter alternate email"
                                    value={form.agency_alternate_mail || ''}
                                    onChange={handleChange}
                                />
                            </div>

                            <div className="space-y-1">
                                <Label htmlFor="agency_website">Website<span className="text-red-500">*</span></Label>
                                <Input
                                    id="agency_website"
                                    type="url"
                                    name="agency_website"
                                    placeholder="https://example.com"
                                    value={form.agency_website}
                                    onChange={handleChange}
                                />
                            </div>

                            <div className="space-y-1">
                                <Label htmlFor="cities">Cities (comma separated) <span className="text-red-500">*</span></Label>
                                <Input
                                    id="cities"
                                    name="cities"
                                    placeholder="e.g. New York, Los Angeles, Chicago"
                                    value={cities}
                                    onChange={handleCitiesChange}
                                    required
                                />
                                <p className="text-xs text-muted-foreground">
                                    Separate multiple cities with commas
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-end space-x-2 mt-6">
                        <Button type="button" variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button type="submit" className="bg-brandPrimary text-white hover:bg-brandPrimary/90">
                            Save Changes
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
};

export default EditVendorCompany;
