// import { domain } from "@/lib/utils";
import { domain } from "@/lib/utils";
import { UserAccount } from "@/types";
import axios from "axios";
import { create } from "zustand";

// Define the types
interface PermissionsStore {
    userAccounts: UserAccount[];
    loading: boolean;
    getUserAccounts: () => Promise<void>;
}

// Create the store
const usePermissionsStore = create<PermissionsStore>()(
    (set) => ({
        userAccounts: [],
        loading: false,
        getUserAccounts: async () => {
            set({ loading: true });
            try {
                // Fetch user accounts from the API or any other source
                const response = await axios.get(`${domain}/api/get-all-organiser-users`)
                const data = response.data.data as UserAccount[];
                set({ userAccounts: data });
            } catch (error) {
                console.error("Error fetching user accounts:", error);
            } finally {
                set({ loading: false });
            }
        },
    })
);

export default usePermissionsStore;
