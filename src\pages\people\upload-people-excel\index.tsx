import { Plus, X, FileSpreadsheet } from 'lucide-react';
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import axios from 'axios';
import { domain2 } from '@/lib/utils';
import { toast } from '@/hooks/use-toast';
import Spinner from '@/components/Spinner';
import * as XLSX from 'xlsx';

// Function to generate and download the Excel file
const generateExcelFile = () => {
    // Define headers for the Excel sheet
    const headers = [
        'firstName', 'lastName', 'linkedinUrl', 'designation', 'company', 'industry',
        'email', 'alternateEmail', 'mobileNumber', 'alternateMobileNumber', 'city'
    ];

    // Define dummy content for the first row
    const dummyRow = [
        'John', 'Doe', 'https://linkedin.com/in/johndoe', 'Software Engineer', 'TechCorp', 'Technology',
        '<EMAIL>', '<EMAIL>', '1234567890', '0987654321', 'New York'
    ];

    // Prepare the data for the Excel sheet
    const data = [headers, dummyRow];

    // Create a new workbook and sheet
    const ws = XLSX.utils.aoa_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

    // Write the file and trigger download
    XLSX.writeFile(wb, 'UserData.xlsx');
};


const UploadPeopleExcel: React.FC = () => {
    const [file, setFile] = useState<File | null>(null);
    const [loading, setLoading] = useState<boolean>(false);

    const onDrop = useCallback((acceptedFiles: File[]) => {
        if (acceptedFiles.length > 0) {
            setFile(acceptedFiles[0]);
        }
    }, []);

    const clearFile = () => {
        setFile(null);
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        onDrop,
        maxFiles: 1, // Limit to one file
        multiple: false, // Prevent selecting multiple files
        accept: {
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [".xlsx"],
            'application/vnd.ms-excel': [".xls"],
        },
    });

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };


    const handleUploadFile = async (file: File) => {
        setLoading(true);
        const response = await axios.post(`${domain2}/api/mapping/v1/people/upload-people-excel`, {
            file
        }, {
            headers: {
                "Content-Type": "multipart/form-data"
            }
        });

        if (response.data.status) {
            const toastContent = (
                <div className="space-y-1 flex justify-between items-center">
                    <p>Inserted {response.data.summary.inserted}, updated {response.data.summary.updated}, skipped {response.data.summary.skipped}, skipped LinkedIn {response.data.summary.skippedLinkedIn ? response.data.summary.skippedLinkedIn : 0}, errors {response.data.summary.errors}</p>
                </div>
            );
            toast({
                title: response.data.message,
                description: toastContent,
                className: "bg-green-800 text-white",
            });
        }
        setLoading(false);
        setFile(null);
    }

    if (loading) {
        return <Spinner />
    }

    return (
        <div className='w-full h-full mt-10 flex flex-col items-center'>
            <Button onClick={generateExcelFile} className='bg-brandPrimary hover:bg-brandPrimaryDark duration-300 text-white absolute right-5'>Download Sample Format</Button>
            {!file ? (
                <div
                    className={`border-2 text-xl border-dashed p-5 text-center cursor-pointer text-neutral-500 border-neutral-500 w-96 h-96 mx-auto rounded-xl flex flex-col items-center justify-center`}
                    {...getRootProps()}
                >
                    <input {...getInputProps()} />
                    {isDragActive ? (
                        <p>Drop your file here ...</p>
                    ) : (
                        <div className="flex flex-col items-center gap-4">
                            <Plus className="h-16 w-16" />
                            <p className="text-lg">Drag 'n' drop a file here, or click to select a file</p>
                            <p className="text-sm text-muted-foreground">Supports: .xlsx, .xls</p>
                        </div>
                    )}
                </div>
            ) : (
                <div className="border rounded-lg p-6 w-full max-w-2xl relative">
                    <div className="flex items-center gap-4">
                        <div className="p-3 rounded-lg bg-primary/10">
                            <FileSpreadsheet className="size-10 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                            <p className="text-lg font-medium text-foreground truncate">{file.name}</p>
                            <p className="text-sm text-muted-foreground">{formatFileSize(file.size)}</p>
                        </div>
                    </div>
                    <Button
                        variant="ghost"
                        size="icon"
                        className="text-muted-foreground hover:text-destructive absolute right-2 top-2"
                        onClick={(e) => {
                            e.stopPropagation();
                            clearFile();
                        }}
                    >
                        <X className="h-4 w-4" />
                    </Button>
                    <div className="mt-6 flex justify-end gap-2">
                        <Button variant="outline" onClick={clearFile}>
                            Cancel
                        </Button>
                        <Button className='bg-green-600 hover:bg-green-700 text-white' onClick={() => handleUploadFile(file)}>Upload File</Button>
                    </div>
                </div>
            )}
        </div>
    );
}

export default UploadPeopleExcel;
