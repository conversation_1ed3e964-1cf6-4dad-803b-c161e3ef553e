import Spinner from '@/components/Spinner';
import { convertTime, domain2, token } from '@/lib/utils';
import useCustomerStore from '@/store/customerStore';
import axios from 'axios';
import React, { useEffect, useState } from 'react';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { Label } from "@/components/ui/label";

import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

import DialogBox from '@/components/DialogBox';
import { View } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { DialogDescription } from '@radix-ui/react-dialog';
import { Input } from '@/components/ui/input';
import { Customer } from '@/types';

const AllUsers: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const setAllCustomers = useCustomerStore(state => state.setAllCustomers);
  const allCustomers = useCustomerStore(state => state.allCustomers);
  const [filteredData, setFilteredData] = useState<Customer[]>([]);

  const [pageNumber, setPageNumber] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10); // Define how many items per page

  const [nameFilter, setNameFilter] = useState<string>("");
  const [designationFilter, setDesignationFilter] = useState<string>("");
  const [companyFilter, setCompanyFilter] = useState<string>("");
  const [emailFilter, setEmailFilter] = useState<string>("");
  const [phoneFilter, setPhoneFilter] = useState<string>("");

  // Logic for pagination
  const indexOfLastItem = pageNumber * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredData?.slice(indexOfFirstItem, indexOfLastItem);

  const totalPages = Math.ceil((filteredData?.length ?? 0) / itemsPerPage);

  useEffect(() => {
    setLoading(true);
    axios.get(`${domain2}/api/v1/user/getAllUsers`, {
      headers: {
        "Authorization": `Bearer ${token}`,
      }
    }).then(res => {
      if (res.data.status) {
        setFilteredData(res.data.result.data);
        console.log(res.data.result.data.length);
        setAllCustomers(res.data.result.data);
      }
    }).finally(() => { setLoading(false); });
  }, []);

  useEffect(() => {
    const filteredCustomers = allCustomers.filter(customer => {
      const fullName = `${customer.first_name || ''} ${customer.last_name || ''}`.toLowerCase();

      const nameMatch = fullName.includes(nameFilter.toLowerCase());
      const emailMatch = (customer.emailId || '').toLowerCase().includes(emailFilter.toLowerCase());
      const companyMatch = (customer.company || '').toLowerCase().includes(companyFilter.toLowerCase());
      const designationMatch = (customer.designation || '').toLowerCase().includes(designationFilter.toLowerCase());
      const phoneMatch = (String(customer.mobileNumber) || '').toLowerCase().includes(phoneFilter.toLowerCase());
  
      // Return the customer if all filters match
      return nameMatch && emailMatch && companyMatch && designationMatch && phoneMatch;
    });
  
    setFilteredData(filteredCustomers);
  }, [nameFilter, designationFilter, companyFilter, emailFilter, phoneFilter]);
  

  if (loading) {
    return <Spinner />
  }

  return (
    <div className='max-w-full w-full sm:p-2'>

      {/* Filters Tab */}
      <div className='flex gap-3 mb-3 overflow-x-scroll p-1'>
        {/* Select Rows */}
        <Select value={itemsPerPage.toString()} onValueChange={(value) => setItemsPerPage(Number(value))}>
          <SelectTrigger className="w-fit">
            <SelectValue placeholder="Show Rows" />
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Select Rows</SelectLabel>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        {/* Filter By Name */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setNameFilter(e.target.value)}
            placeholder="Filter by name"
          />
        </div>

        {/* Filter By Email */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setEmailFilter(e.target.value)}
            placeholder="Filter by Email"
          />
        </div>

        {/* Filter By Phone No. */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setPhoneFilter(e.target.value)}
            placeholder="Filter by Phone No."
          />
        </div>

        {/* Filter By Company */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setCompanyFilter(e.target.value)}
            placeholder="Filter by Company"
          />
        </div>

        {/* Filter By Designation */}
        <div>
          <Input
            type="text"
            className="max-w-60 min-w-36"
            onChange={(e) => setDesignationFilter(e.target.value)}
            placeholder="Filter by designation"
          />
        </div>
      </div>

      <p className='text-right text-popover-foreground opacity-50'>Total Users {allCustomers.length}</p>
      <Table className='text-base text-nowrap'>
        <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
          <TableRow>
            <TableHead className='text-white'>S.No</TableHead>
            <TableHead className='text-white'>Name</TableHead>
            <TableHead className='text-white'>Email</TableHead>
            <TableHead className='text-white'>Phone Number</TableHead>
            <TableHead className='text-white'>Company</TableHead>
            <TableHead className='text-white'>Designation</TableHead>
            <TableHead className="text-white !w-fit">City</TableHead>
            <TableHead className="text-white !w-fit">Date & Time</TableHead>
            <TableHead className="text-white">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentItems?.map((data, index: number) => (
            <TableRow key={data._id}>
              <TableCell>{index + 1}</TableCell>
              <TableCell>{data.first_name + " " + data.last_name}</TableCell>
              <TableCell>{data.emailId}</TableCell>
              <TableCell>{data.mobileNumber}</TableCell>
              <TableCell>{data.company}</TableCell>
              <TableCell>{data.designation}</TableCell>
              <TableCell>{data.city}</TableCell>
              <TableCell>{convertTime(data.createdAt)}</TableCell>
              <TableCell className="flex justify-end gap-2">
                {/* <EditDialogBox onSubmit={print} value={data.company} title='Edit Company' /> */}
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="ghost"
                      className={`p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-green-600/50 bg-green-500`}
                    >
                      <View />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className='max-w-xl max-h-[30rem] overflow-scroll'>
                    <DialogDescription></DialogDescription>
                    <DialogTitle className='text-2xl text-center mb-5 sticky top-0 bg-background w-full'>{`User ${data.first_name + " " + data.last_name}`}</DialogTitle>

                    <div className='space-y-5 text-lg'>
                      {/* First Row */}
                      <div className='flex justify-between flex-col min-[460px]:flex-row gap-5'>
                        {/* Name */}
                        <div className='w-full'>
                          <Label htmlFor='name' className='text-sm text-muted-foreground font-normal'>Name</Label>
                          <p>{data.first_name + " " + data.last_name || "Not Found"}</p>
                        </div>

                        {/* Mobile Number */}
                        <div className='w-full'>
                          <Label htmlFor='phone' className='text-sm text-muted-foreground font-normal'>Mobile Number</Label>
                          <p>{data.mobileNumber || "Not Found"}</p>
                        </div>
                      </div>

                      {/* Second Row */}
                      <div className='flex justify-between flex-col min-[460px]:flex-row gap-5'>
                        {/* Company */}
                        <div className='w-full'>
                          <Label htmlFor='company' className='text-sm text-muted-foreground font-normal'>Company</Label>
                          <p>{data.company || "Not Found"}</p>
                        </div>

                        {/* City */}
                        <div className='w-full'>
                          <Label htmlFor='city' className='text-sm text-muted-foreground font-normal'>City</Label>
                          <p>{data.city || "Not Found"}</p>
                        </div>
                      </div>

                      {/* Third Row */}
                      <div className='flex justify-between flex-col min-[460px]:flex-row gap-5'>
                        {/* Designation */}
                        <div className='w-full'>
                          <Label htmlFor='designation' className='text-sm text-muted-foreground font-normal'>Designation</Label>
                          <p>{data.designation || "Not Found"}</p>
                        </div>

                        {/* Industry */}
                        <div className='w-full'>
                          <Label htmlFor='industry' className='text-sm text-muted-foreground font-normal'>Industry</Label>
                          <p>{data.industryName || "Not Found"}</p>
                        </div>
                      </div>

                      {/* Fourth Row */}
                      <div className='flex justify-between flex-col min-[460px]:flex-row gap-5'>
                        {/* Latitude */}
                        <div className='w-full'>
                          <Label htmlFor='latitude' className='text-sm text-muted-foreground font-normal'>Latitude</Label>
                          <p>{data.latitude || "Not Found"}</p>
                        </div>

                        {/* Longitude */}
                        <div className='w-full'>
                          <Label htmlFor='longitude' className='text-sm text-muted-foreground font-normal'>Longitude</Label>
                          <p>{data.longitude || "Not Found"}</p>
                        </div>
                      </div>

                      {/* Fifth Row */}
                      <div className='flex justify-between gap-5'>
                        {/* Status */}
                        <div className='w-full'>
                          <Label htmlFor='status' className='text-sm text-muted-foreground font-normal'>Status</Label>
                          <p>{data.status || "Not Found"}</p>
                        </div>
                      </div>
                    </div>


                  </DialogContent>
                </Dialog>
                <DialogBox
                  buttonText='Block'
                  description='This user will be blocked'
                  onSubmit={() => { }}
                  trigger='block'
                  title={`Block user ${data.first_name + " " + data.last_name} ?`} />
                <DialogBox
                  buttonText='Delete'
                  description='This user will be deleted forever'
                  onSubmit={() => { }}
                  trigger='delete'
                  title={`Delete user ${data.first_name + " " + data.last_name} ?`} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <div className='mt-10'>
        <Pagination>
          <PaginationContent>
            {/* Previous Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationPrevious
                onClick={() => setPageNumber((prev) => Math.max(1, prev - 1))}
                className={pageNumber === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === 1}
              >
                Prev
              </PaginationPrevious>
            </PaginationItem>

            {/* First page */}
            <PaginationItem className="cursor-pointer">
              <PaginationLink
                onClick={() => setPageNumber(1)}
                isActive={pageNumber === 1}
              >
                1
              </PaginationLink>
            </PaginationItem>

            {/* Ellipsis before the current range */}
            {pageNumber > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Dynamic Page Numbers */}
            {Array.from({ length: 3 }, (_, index) => {
              const page = pageNumber - 1 + index;
              if (page > 1 && page < totalPages) {
                return (
                  <PaginationItem className="cursor-pointer" key={page}>
                    <PaginationLink
                      isActive={pageNumber === page}
                      onClick={() => setPageNumber(page)}
                    >
                      {page}
                    </PaginationLink>
                  </PaginationItem>
                );
              }
              return null;
            })}

            {/* Ellipsis after the current range */}
            {pageNumber < totalPages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Last page */}
            {totalPages > 1 && (
              <PaginationItem className="cursor-pointer">
                <PaginationLink
                  onClick={() => setPageNumber(totalPages)}
                  isActive={pageNumber === totalPages}
                >
                  {totalPages}
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Next Button */}
            <PaginationItem className="cursor-pointer">
              <PaginationNext
                onClick={() => setPageNumber((prev) => Math.min(totalPages, prev + 1))}
                className={pageNumber === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                aria-disabled={pageNumber === totalPages}
              >
                Next
              </PaginationNext>
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    </div>
  )
}

export default AllUsers;