import { Input } from '@/components/ui/input';
import React, { useEffect, useState } from 'react';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";

import { domain2 } from '@/lib/utils';
import axios from 'axios';
import Spinner from '@/components/Spinner';
import { Button } from '@/components/ui/button';
import { FileSpreadsheet, SquarePen, Save } from 'lucide-react';
import { Label } from "@/components/ui/label";
import { toast } from '@/hooks/use-toast';

import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogTitle,
} from "@/components/ui/dialog";

// Define the Person interface based on the table structure
interface Person {
    _id: string;
    firstName: string;
    lastName: string;
    company: string;
    designation: string;
    industry: string;
    linkedinUrl: string;
    email: string;
    alternateEmail: string;
    mobileNumber: string;
    alternateMobileNumber: string;
    city: string;
}


const fetchPeople = async (page: number, firstName: string, lastName: string, industry: string, company: string, designation: string, city: string) => {
    try {
        const response = await axios.get(`${domain2}/api/mapping/v1/people/all-people?page=${page}&firstName=${firstName}&lastName=${lastName}&industry=${industry}&company=${company}&designation=${designation}&city=${city}`);
        return response.data;
    } catch (error) {
        return error;
    }
}

const ViewPeople: React.FC = () => {

    const [filters, setFilters] = React.useState({
        page: 1,
        firstName: '',
        lastName: '',
        email: '',
        alternateEmail: '',
        mobileNumber: '',
        alternateMobileNumber: '',
        industry: '',
        company: '',
        desgination: '',
        city: '',
        linkedinUrl: '',
    });

    const [totalPages, setTotalPages] = React.useState(0);

    const [loading, setLoading] = React.useState(false);
    const [people, setPeople] = React.useState<Person[]>([]);
    const [totalPeople, setTotalPeople] = useState<number>(0);

    // Edit dialog state
    const [isEditDialogOpen, setIsEditDialogOpen] = useState<boolean>(false);
    const [selectedPerson, setSelectedPerson] = useState<Person | null>(null);
    const [editFormData, setEditFormData] = useState<Person>({
        _id: '',
        firstName: '',
        lastName: '',
        company: '',
        designation: '',
        industry: '',
        linkedinUrl: '',
        email: '',
        alternateEmail: '',
        mobileNumber: '',
        alternateMobileNumber: '',
        city: '',
    });

    // Handle opening edit dialog
    const handleEditClick = (person: Person) => {
        console.log('Original person data:', person);
        setSelectedPerson(person);
        setEditFormData({ ...person });
        setIsEditDialogOpen(true);
    };

    // Handle form input changes
    const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { id, value } = e.target;
        setEditFormData(prev => ({
            ...prev,
            [id]: value,
        }));
    };

    // Handle form submission
    const handleEditSubmit = async () => {
        if (!selectedPerson) return;
        
        try {
            const response = await axios.patch(`${domain2}/api/mapping/v1/people/update-people/${selectedPerson._id}`, editFormData, {
                headers: {
                    "Content-Type": "application/json"
                }
            });

            if (response.data.status) {
                toast({
                    title: response.data.message,
                    className: "bg-green-800 text-white",
                });
                setIsEditDialogOpen(false);
                setSelectedPerson(null);
            } else {
                toast({
                    title: response.data.message,
                    variant: "destructive",
                });
            }
        } catch (error) {
            toast({
                title: "Failed to update person",
                variant: "destructive",
            });
        }

        // Show success toast and close dialog
        toast({
            title: "Person Updated Successfully",
            description: "The person's information has been updated.",
            className: "bg-green-800 text-white",
        });

        // Close dialog and reset state
        setIsEditDialogOpen(false);
        setSelectedPerson(null);
    };

    const handleExport = () => {
        // Define the headers for the CSV file - match table headers exactly
        const headers = ['S.No', 'First Name', 'Last Name', 'Company', 'Designation', 'Industry', 'LinkedIn', 'Email', 'Alternate Email', 'Mobile Number', 'Alternate Mobile Number', 'City'];

        // Map the people data to match table columns exactly
        const rows = people.map((person: Person, index: number) => [
            index + 1,
            person?.firstName ?? '-',
            person?.lastName ?? '-',
            person?.company ?? '-',
            person?.designation ?? '-',
            person?.industry ?? '-',
            person?.linkedinUrl ?? '-',
            person?.email ?? '-',
            person?.alternateEmail ?? '-',
            person?.mobileNumber ?? '-',
            person?.alternateMobileNumber ?? '-',
            person?.city ?? '-'
        ]);

        // Combine headers and rows into a single array
        const csvData = [headers, ...rows];

        // Convert the array into a CSV string
        const csvString = csvData
            .map(row => row.map(cell => `"${cell}"`).join(',')) // Wrap cells in quotes to handle commas
            .join('\n');

        // Create a Blob object with UTF-8 encoding
        const blob = new Blob(['\ufeff' + csvString], { type: 'text/csv;charset=utf-8' });

        // Create a download link
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'PeopleData.csv';

        // Trigger the download
        link.click();
    };

    useEffect(() => {
        setLoading(true);
        fetchPeople(filters.page, filters.firstName, filters.lastName, filters.industry, filters.company, filters.desgination, filters.city).then((data) => {
            const pages = Math.ceil(data.data.totalPeople / 50);
            setTotalPeople(data.data.totalPeople);
            setTotalPages(pages);
            setPeople(data.data.peoples);
            setLoading(false);
        });
    }, [filters, ]);

    return (
        <div className='h-full w-full'>
            <div className='flex gap-3'>
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.firstName}
                    onChange={(e) => setFilters({ ...filters, firstName: e.target.value, page: 1 })}
                    placeholder="Filter by first name"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.lastName}
                    onChange={(e) => setFilters({ ...filters, lastName: e.target.value, page: 1 })}
                    placeholder="Filter by last name"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.company}
                    onChange={(e) => setFilters({ ...filters, company: e.target.value, page: 1 })}
                    placeholder="Filter by company"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.desgination}
                    onChange={(e) => setFilters({ ...filters, desgination: e.target.value, page: 1 })}
                    placeholder="Filter by designation"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.industry}
                    onChange={(e) => setFilters({ ...filters, industry: e.target.value, page: 1 })}
                    placeholder="Filter by industry"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.city}
                    onChange={(e) => setFilters({ ...filters, city: e.target.value, page: 1 })}
                    placeholder="Filter by city"
                />
            </div>

            {loading ?
                <div className='h-full flex-1 w-full grid place-items-center'><Spinner /></div>
                :
                <div className='mt-3'>
                    <div className='flex gap-3 justify-end items-center mb-3 mt-5'>
                        <p className='text-right text-sm'>Total People: {totalPeople}</p>
                        <Button onClick={handleExport} className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>Export <FileSpreadsheet /></Button>
                    </div>
                    <Table className='text-base'>
                        {/* <TableCaption>A list of people</TableCaption> */}
                        <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
                            <TableRow>
                                <TableHead className="text-white">S.No</TableHead>
                                <TableHead className='text-white'>First Name</TableHead>
                                <TableHead className='text-white'>Last Name</TableHead>
                                <TableHead className="text-white">Company</TableHead>
                                <TableHead className="text-white">Designation</TableHead>
                                <TableHead className="text-white">Industry</TableHead>
                                <TableHead className="text-white">LinkedIn</TableHead>
                                <TableHead className="text-white">Email</TableHead>
                                <TableHead className="text-white">Alternate Email</TableHead>
                                <TableHead className="text-white">Mobile Number</TableHead>
                                <TableHead className="text-white">Alternate Mobile Number</TableHead>
                                <TableHead className="text-white">City</TableHead>
                                <TableHead className="text-white">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {people.map((person: Person, index: number) => (
                                <TableRow key={person._id}>
                                    <TableCell className='capitalize'>{index + 1}</TableCell>
                                    <TableCell className='capitalize'>{person.firstName}</TableCell>
                                    <TableCell className='capitalize'>{person.lastName}</TableCell>
                                    <TableCell className='capitalize'>{person.company}</TableCell>
                                    <TableCell className='capitalize'>{person.designation}</TableCell>
                                    <TableCell className='capitalize'>{person.industry}</TableCell>
                                    <TableCell>{person.linkedinUrl}</TableCell>
                                    <TableCell>{person.email}</TableCell>
                                    <TableCell>{person.alternateEmail}</TableCell>
                                    <TableCell>{person.mobileNumber}</TableCell>
                                    <TableCell>{person.alternateMobileNumber}</TableCell>
                                    <TableCell className='capitalize'>{person.city}</TableCell>

                                    <TableCell>
                                        <Button
                                            variant={"ghost"}
                                            className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-cyan-600/50 bg-cyan-500"
                                            onClick={() => handleEditClick(person)}
                                        >
                                            <SquarePen />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>

                </div>}

            {/* Edit Dialog - Single dialog for all edit operations */}
            <Dialog
                open={isEditDialogOpen}
                onOpenChange={(open) => {
                    setIsEditDialogOpen(open);
                    if (!open) {
                        setSelectedPerson(null);
                    }
                }}
            >
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-scroll py-0">
                    <DialogDescription></DialogDescription>
                    <DialogTitle className="text-2xl text-center mb-5 sticky top-0 bg-background w-full py-5">
                        Edit Person
                    </DialogTitle>

                    <div className="space-y-5">
                        {/* First Row - Name Fields */}
                        <div className="flex flex-col min-[460px]:flex-row justify-between gap-5">
                            <div className="w-full">
                                <Label htmlFor="firstName" className="text-sm text-muted-foreground font-normal">
                                    First Name
                                </Label>
                                <Input
                                    id="firstName"
                                    type="text"
                                    value={editFormData.firstName}
                                    onChange={handleEditInputChange}
                                    placeholder="First Name"
                                />
                            </div>
                            <div className="w-full">
                                <Label htmlFor="lastName" className="text-sm text-muted-foreground font-normal">
                                    Last Name
                                </Label>
                                <Input
                                    id="lastName"
                                    type="text"
                                    value={editFormData.lastName}
                                    onChange={handleEditInputChange}
                                    placeholder="Last Name"
                                />
                            </div>
                        </div>

                        {/* Second Row - Company and Designation */}
                        <div className="flex flex-col min-[460px]:flex-row justify-between gap-5">
                            <div className="w-full">
                                <Label htmlFor="company" className="text-sm text-muted-foreground font-normal">
                                    Company
                                </Label>
                                <Input
                                    id="company"
                                    type="text"
                                    value={editFormData.company}
                                    onChange={handleEditInputChange}
                                    placeholder="Company"
                                />
                            </div>
                            <div className="w-full">
                                <Label htmlFor="designation" className="text-sm text-muted-foreground font-normal">
                                    Designation
                                </Label>
                                <Input
                                    id="designation"
                                    type="text"
                                    value={editFormData.designation}
                                    onChange={handleEditInputChange}
                                    placeholder="Designation"
                                />
                            </div>
                        </div>

                        {/* Third Row - Industry and City */}
                        <div className="flex flex-col min-[460px]:flex-row justify-between gap-5">
                            <div className="w-full">
                                <Label htmlFor="industry" className="text-sm text-muted-foreground font-normal">
                                    Industry
                                </Label>
                                <Input
                                    id="industry"
                                    type="text"
                                    value={editFormData.industry}
                                    onChange={handleEditInputChange}
                                    placeholder="Industry"
                                />
                            </div>
                            <div className="w-full">
                                <Label htmlFor="city" className="text-sm text-muted-foreground font-normal">
                                    City
                                </Label>
                                <Input
                                    id="city"
                                    type="text"
                                    value={editFormData.city}
                                    onChange={handleEditInputChange}
                                    placeholder="City"
                                />
                            </div>
                        </div>

                        {/* Fourth Row - LinkedIn URL */}
                        <div className="w-full">
                            <Label htmlFor="linkedinUrl" className="text-sm text-muted-foreground font-normal">
                                LinkedIn URL
                            </Label>
                            <Input
                                id="linkedinUrl"
                                type="text"
                                value={editFormData.linkedinUrl}
                                onChange={handleEditInputChange}
                                placeholder="LinkedIn URL"
                            />
                        </div>

                        {/* Fifth Row - Email Fields */}
                        <div className="flex flex-col min-[460px]:flex-row justify-between gap-5">
                            <div className="w-full">
                                <Label htmlFor="email" className="text-sm text-muted-foreground font-normal">
                                    Email
                                </Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={editFormData.email}
                                    onChange={handleEditInputChange}
                                    placeholder="Email"
                                />
                            </div>
                            <div className="w-full">
                                <Label htmlFor="alternateEmail" className="text-sm text-muted-foreground font-normal">
                                    Alternate Email
                                </Label>
                                <Input
                                    id="alternateEmail"
                                    type="email"
                                    value={editFormData.alternateEmail}
                                    onChange={handleEditInputChange}
                                    placeholder="Alternate Email"
                                />
                            </div>
                        </div>

                        {/* Sixth Row - Mobile Number Fields */}
                        <div className="flex flex-col min-[460px]:flex-row justify-between gap-5">
                            <div className="w-full">
                                <Label htmlFor="mobileNumber" className="text-sm text-muted-foreground font-normal">
                                    Mobile Number
                                </Label>
                                <Input
                                    id="mobileNumber"
                                    type="text"
                                    value={editFormData.mobileNumber}
                                    onChange={handleEditInputChange}
                                    placeholder="Mobile Number"
                                />
                            </div>
                            <div className="w-full">
                                <Label htmlFor="alternateMobileNumber" className="text-sm text-muted-foreground font-normal">
                                    Alternate Mobile Number
                                </Label>
                                <Input
                                    id="alternateMobileNumber"
                                    type="text"
                                    value={editFormData.alternateMobileNumber}
                                    onChange={handleEditInputChange}
                                    placeholder="Alternate Mobile Number"
                                />
                            </div>
                        </div>
                    </div>

                    <div className="py-5 bg-background sticky bottom-0">
                        <Button
                            onClick={handleEditSubmit}
                            className="bg-brandPrimary hover:bg-brandPrimaryDark text-white w-full"
                        >
                            Update <Save />
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>

            <div className='mt-10'>
                <Pagination>
                    <PaginationContent>
                        {/* Previous Button */}
                        <PaginationItem className="cursor-pointer">
                            <PaginationPrevious
                                onClick={() => setFilters({ ...filters, page: filters.page - 1 })}
                                className={filters.page === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={filters.page === 1}
                            >
                                Prev
                            </PaginationPrevious>
                        </PaginationItem>

                        {/* First page */}
                        <PaginationItem className="cursor-pointer">
                            <PaginationLink
                                onClick={() => setFilters({ ...filters, page: 1 })}
                                isActive={filters.page === 1}
                            >
                                1
                            </PaginationLink>
                        </PaginationItem>

                        {/* Ellipsis before the current range */}
                        {filters.page > 3 && (
                            <PaginationItem>
                                <PaginationEllipsis />
                            </PaginationItem>
                        )}

                        {/* Dynamic Page Numbers */}
                        {Array.from({ length: 3 }, (_, index) => {
                            const page = filters.page - 1 + index;
                            if (page > 1 && page < totalPages) {
                                return (
                                    <PaginationItem className="cursor-pointer" key={page}>
                                        <PaginationLink
                                            isActive={filters.page === page}
                                            onClick={() => setFilters({ ...filters, page: page })}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                );
                            }
                            return null;
                        })}

                        {/* Ellipsis after the current range */}
                        {filters.page < totalPages - 2 && (
                            <PaginationItem>
                                <PaginationEllipsis />
                            </PaginationItem>
                        )}

                        {/* Last page */}
                        {totalPages > 1 && (
                            <PaginationItem className="cursor-pointer">
                                <PaginationLink
                                    onClick={() => setFilters({ ...filters, page: totalPages })}
                                    isActive={filters.page === totalPages}
                                >
                                    {totalPages}
                                </PaginationLink>
                            </PaginationItem>
                        )}

                        {/* Next Button */}
                        <PaginationItem className="cursor-pointer">
                            <PaginationNext
                                onClick={() => setFilters({ ...filters, page: filters.page + 1 })}
                                className={filters.page === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={filters.page === totalPages}
                            >
                                Next
                            </PaginationNext>
                        </PaginationItem>
                    </PaginationContent>
                </Pagination>

            </div>
        </div>
    )
}

export default ViewPeople;
