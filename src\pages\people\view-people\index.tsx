import { Input } from '@/components/ui/input';
import React, { useEffect, useState } from 'react';

import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";

import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";

import { domain2 } from '@/lib/utils';
import axios from 'axios';
import Spinner from '@/components/Spinner';
import { Button } from '@/components/ui/button';
import { FileSpreadsheet, SquarePen } from 'lucide-react';

import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog";


const fetchPeople = async (page: number, firstName: string, lastName: string, industry: string, company: string, designation: string, city: string) => {
    try {
        const response = await axios.get(`${domain2}/api/mapping/v1/people/all-people?page=${page}&firstName=${firstName}&lastName=${lastName}&industry=${industry}&company=${company}&designation=${designation}&city=${city}`);
        return response.data;
    } catch (error) {
        return error;
    }
}

const ViewPeople: React.FC = () => {

    const [filters, setFilters] = React.useState({
        page: 1,
        firstName: '',
        lastName: '',
        email: '',
        alternateEmail: '',
        mobileNumber: '',
        alternateMobileNumber: '',
        industry: '',
        company: '',
        desgination: '',
        city: '',
        linkedinUrl: '',
    });

    const [totalPages, setTotalPages] = React.useState(0);

    const [loading, setLoading] = React.useState(false);
    const [people, setPeople] = React.useState([]);
    const [totalPeople, setTotalPeople] = useState<number>(0);

    const handleExport = () => {
        // Define the headers for the CSV file - match table headers exactly
        const headers = ['S.No', 'First Name', 'Last Name', 'Company', 'Designation', 'Industry', 'LinkedIn', 'Email', 'Alternate Email', 'Mobile Number', 'Alternate Mobile Number', 'City'];

        // Map the people data to match table columns exactly
        const rows = people.map((person: any, index: number) => [
            index + 1,
            person?.firstName ?? '-',
            person?.lastName ?? '-',
            person?.company ?? '-',
            person?.designation ?? '-',
            person?.industry ?? '-',
            person?.linkedinUrl ?? '-',
            person?.email ?? '-',
            person?.alternateEmail ?? '-',
            person?.mobileNumber ?? '-',
            person?.alternateMobileNumber ?? '-',
            person?.city ?? '-'
        ]);

        // Combine headers and rows into a single array
        const csvData = [headers, ...rows];

        // Convert the array into a CSV string
        const csvString = csvData
            .map(row => row.map(cell => `"${cell}"`).join(',')) // Wrap cells in quotes to handle commas
            .join('\n');

        // Create a Blob object with UTF-8 encoding
        const blob = new Blob(['\ufeff' + csvString], { type: 'text/csv;charset=utf-8' });

        // Create a download link
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = 'PeopleData.csv';

        // Trigger the download
        link.click();
    };

    useEffect(() => {
        setLoading(true);
        fetchPeople(filters.page, filters.firstName, filters.lastName, filters.industry, filters.company, filters.desgination, filters.city).then((data) => {
            const pages = Math.ceil(data.data.totalPeople / 50);
            setTotalPeople(data.data.totalPeople);
            setTotalPages(pages);
            setPeople(data.data.peoples);
            setLoading(false);
        });
    }, [filters]);

    return (
        <div className='h-full w-full'>
            <div className='flex gap-3'>
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.firstName}
                    onChange={(e) => setFilters({ ...filters, firstName: e.target.value, page: 1 })}
                    placeholder="Filter by first name"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.lastName}
                    onChange={(e) => setFilters({ ...filters, lastName: e.target.value, page: 1 })}
                    placeholder="Filter by last name"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.company}
                    onChange={(e) => setFilters({ ...filters, company: e.target.value, page: 1 })}
                    placeholder="Filter by company"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.desgination}
                    onChange={(e) => setFilters({ ...filters, desgination: e.target.value, page: 1 })}
                    placeholder="Filter by designation"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.industry}
                    onChange={(e) => setFilters({ ...filters, industry: e.target.value, page: 1 })}
                    placeholder="Filter by industry"
                />
                <Input
                    type="text"
                    className="max-w-60"
                    value={filters.city}
                    onChange={(e) => setFilters({ ...filters, city: e.target.value, page: 1 })}
                    placeholder="Filter by city"
                />
            </div>

            {loading ?
                <div className='h-full flex-1 w-full grid place-items-center'><Spinner /></div>
                :
                <div className='mt-3'>
                    <div className='flex gap-3 justify-end items-center mb-3 mt-5'>
                        <p className='text-right text-sm'>Total People: {totalPeople}</p>
                        <Button onClick={handleExport} className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>Export <FileSpreadsheet /></Button>
                    </div>
                    <Table className='text-base'>
                        {/* <TableCaption>A list of people</TableCaption> */}
                        <TableHeader className='bg-brandPrimary hover:bg-brandPrimaryDark'>
                            <TableRow>
                                <TableHead className="text-white">S.No</TableHead>
                                <TableHead className='text-white'>First Name</TableHead>
                                <TableHead className='text-white'>Last Name</TableHead>
                                <TableHead className="text-white">Company</TableHead>
                                <TableHead className="text-white">Designation</TableHead>
                                <TableHead className="text-white">Industry</TableHead>
                                <TableHead className="text-white">LinkedIn</TableHead>
                                <TableHead className="text-white">Email</TableHead>
                                <TableHead className="text-white">Alternate Email</TableHead>
                                <TableHead className="text-white">Mobile Number</TableHead>
                                <TableHead className="text-white">Alternate Mobile Number</TableHead>
                                <TableHead className="text-white">City</TableHead>
                                <TableHead className="text-white">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {people.map((person: any, index: number) => (
                                <TableRow key={person.id}>
                                    <TableCell className='capitalize'>{index + 1}</TableCell>
                                    <TableCell className='capitalize'>{person.firstName}</TableCell>
                                    <TableCell className='capitalize'>{person.lastName}</TableCell>
                                    <TableCell className='capitalize'>{person.company}</TableCell>
                                    <TableCell className='capitalize'>{person.designation}</TableCell>
                                    <TableCell className='capitalize'>{person.industry}</TableCell>
                                    <TableCell>{person.linkedinUrl}</TableCell>
                                    <TableCell>{person.email}</TableCell>
                                    <TableCell>{person.alternateEmail}</TableCell>
                                    <TableCell>{person.mobileNumber}</TableCell>
                                    <TableCell>{person.alternateMobileNumber}</TableCell>
                                    <TableCell className='capitalize'>{person.city}</TableCell>


                                    <TableCell>
                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Button
                                                    variant={"ghost"}
                                                    className="p-3 h-5 w-5 rounded-full text-white hover:text-white hover:bg-cyan-600/50 bg-cyan-500"
                                                    // onClick={() => {
                                                    //     setSelectedDesignation(data);
                                                    //     setIsUpdateDialogOpen(true);
                                                    // }}
                                                >
                                                    <SquarePen />
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent>
                                                <DialogHeader>
                                                    <DialogTitle>Are you absolutely sure?</DialogTitle>
                                                    <DialogDescription>
                                                        This action cannot be undone. This will permanently delete your account and remove your data from our servers.
                                                    </DialogDescription>
                                                </DialogHeader>
                                                <DialogFooter>
                                                    <DialogClose asChild>
                                                        <Button variant="outline">Cancel</Button>
                                                    </DialogClose>
                                                    <DialogClose asChild>
                                                        <Button className='bg-brandPrimary hover:bg-brandPrimaryDark text-white'>Continue</Button>
                                                    </DialogClose>
                                                </DialogFooter>
                                            </DialogContent>
                                        </Dialog>
                                    </TableCell>


                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>

                </div>}

            <div className='mt-10'>
                <Pagination>
                    <PaginationContent>
                        {/* Previous Button */}
                        <PaginationItem className="cursor-pointer">
                            <PaginationPrevious
                                onClick={() => setFilters({ ...filters, page: filters.page - 1 })}
                                className={filters.page === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={filters.page === 1}
                            >
                                Prev
                            </PaginationPrevious>
                        </PaginationItem>

                        {/* First page */}
                        <PaginationItem className="cursor-pointer">
                            <PaginationLink
                                onClick={() => setFilters({ ...filters, page: 1 })}
                                isActive={filters.page === 1}
                            >
                                1
                            </PaginationLink>
                        </PaginationItem>

                        {/* Ellipsis before the current range */}
                        {filters.page > 3 && (
                            <PaginationItem>
                                <PaginationEllipsis />
                            </PaginationItem>
                        )}

                        {/* Dynamic Page Numbers */}
                        {Array.from({ length: 3 }, (_, index) => {
                            const page = filters.page - 1 + index;
                            if (page > 1 && page < totalPages) {
                                return (
                                    <PaginationItem className="cursor-pointer" key={page}>
                                        <PaginationLink
                                            isActive={filters.page === page}
                                            onClick={() => setFilters({ ...filters, page: page })}
                                        >
                                            {page}
                                        </PaginationLink>
                                    </PaginationItem>
                                );
                            }
                            return null;
                        })}

                        {/* Ellipsis after the current range */}
                        {filters.page < totalPages - 2 && (
                            <PaginationItem>
                                <PaginationEllipsis />
                            </PaginationItem>
                        )}

                        {/* Last page */}
                        {totalPages > 1 && (
                            <PaginationItem className="cursor-pointer">
                                <PaginationLink
                                    onClick={() => setFilters({ ...filters, page: totalPages })}
                                    isActive={filters.page === totalPages}
                                >
                                    {totalPages}
                                </PaginationLink>
                            </PaginationItem>
                        )}

                        {/* Next Button */}
                        <PaginationItem className="cursor-pointer">
                            <PaginationNext
                                onClick={() => setFilters({ ...filters, page: filters.page + 1 })}
                                className={filters.page === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                                aria-disabled={filters.page === totalPages}
                            >
                                Next
                            </PaginationNext>
                        </PaginationItem>
                    </PaginationContent>
                </Pagination>

            </div>
        </div>
    )
}

export default ViewPeople;
